import {describe, it, expect} from "vitest";
import {
    doRulesOverlap,
    findOverlappingAssignments,
    type OverlapDetail,
} from "./overlap-detection";
import type {AssignmentRule, Individual, Team} from "../types/lead-assignment";

describe("doRulesOverlap", () => {
    describe("basic overlap detection", () => {
        it("should detect overlap when both rules have 'Any' criteria", () => {
            const rule1: AssignmentRule = {
                id: "rule1",
                criteria: {
                    geography: {type: "Any"},
                    eventType: {type: "Any"},
                },
            };

            const rule2: AssignmentRule = {
                id: "rule2",
                criteria: {
                    geography: {type: "Any"},
                    eventType: {type: "Any"},
                },
            };

            expect(doRulesOverlap(rule1, rule2)).toBe(true);
        });

        it("should detect overlap when rules share specific values", () => {
            const rule1: AssignmentRule = {
                id: "rule1",
                criteria: {
                    geography: {type: "Specific", values: ["USA", "Canada"]},
                    eventType: {type: "Specific", values: ["Wedding", "Corporate"]},
                },
            };

            const rule2: AssignmentRule = {
                id: "rule2",
                criteria: {
                    geography: {type: "Specific", values: ["USA", "Mexico"]},
                    eventType: {type: "Specific", values: ["Wedding", "Social"]},
                },
            };

            // They overlap because USA and Wedding are common
            expect(doRulesOverlap(rule1, rule2)).toBe(true);
        });

        it("should not detect overlap when rules have no common values", () => {
            const rule1: AssignmentRule = {
                id: "rule1",
                criteria: {
                    geography: {type: "Specific", values: ["USA", "Canada"]},
                    eventType: {type: "Specific", values: ["Wedding"]},
                },
            };

            const rule2: AssignmentRule = {
                id: "rule2",
                criteria: {
                    geography: {type: "Specific", values: ["Mexico", "Brazil"]},
                    eventType: {type: "Specific", values: ["Corporate"]},
                },
            };

            expect(doRulesOverlap(rule1, rule2)).toBe(false);
        });

        it("should handle mix of Any and Specific criteria", () => {
            const rule1: AssignmentRule = {
                id: "rule1",
                criteria: {
                    geography: {type: "Any"},
                    eventType: {type: "Specific", values: ["Wedding"]},
                },
            };

            const rule2: AssignmentRule = {
                id: "rule2",
                criteria: {
                    geography: {type: "Specific", values: ["USA"]},
                    eventType: {type: "Specific", values: ["Wedding", "Corporate"]},
                },
            };

            // They overlap because both have Wedding and geography overlaps (Any matches USA)
            expect(doRulesOverlap(rule1, rule2)).toBe(true);
        });

        it("should handle null criteria - rules with different sets of criteria don't overlap", () => {
            const rule1: AssignmentRule = {
                id: "rule1",
                criteria: {
                    geography: {type: "Specific", values: ["USA"]},
                    eventType: null,
                },
            };

            const rule2: AssignmentRule = {
                id: "rule2",
                criteria: {
                    geography: {type: "Specific", values: ["USA"]},
                    eventType: {type: "Specific", values: ["Wedding"]},
                },
            };

            // They should NOT overlap because rule2 has eventType criteria that rule1 doesn't have
            // This aligns with the user's requirement that entities without certain criteria
            // shouldn't conflict with entities that have those criteria
            expect(doRulesOverlap(rule1, rule2)).toBe(false);
        });

        it("should not overlap when one rule has all null criteria", () => {
            const rule1: AssignmentRule = {
                id: "rule1",
                criteria: {
                    eventType: null,
                    industry: null,
                },
            };

            const rule2: AssignmentRule = {
                id: "rule2",
                criteria: {
                    eventType: {type: "Specific", values: ["Wedding"]},
                    industry: {type: "Specific", values: ["Technology"]},
                },
            };

            // They should NOT overlap because rule1 has all null criteria
            // There's nothing to match on
            expect(doRulesOverlap(rule1, rule2)).toBe(false);
        });

        it("should detect overlap in team assignment mode with inherited rules", () => {
            const teams: Team[] = [
                {
                    id: "sales-team",
                    name: "Sales",
                    rules: [
                        {
                            id: "team-rule",
                            criteria: {
                                eventType: {type: "Specific", values: ["Social Event"]},
                                industry: {type: "Any"},
                            },
                        },
                    ],
                },
            ];

            const individuals: Individual[] = [
                {
                    id: "1",
                    name: "John Smith",
                    title: "Sales Manager",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: null, // Standalone
                    rules: [
                        {
                            id: "a5ea3427-f609-41b0-ab47-5c5ec0e53b24",
                            criteria: {
                                eventType: {type: "Any"},
                                industry: {type: "Specific", values: ["Healthcare", "Finance", "Education"]},
                            },
                        },
                    ],
                },
                {
                    id: "4d3b7f01-c1bd-496f-989a-e07f6dd46f2b",
                    name: "Joe Blow",
                    title: "",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: "sales-team", // Team member
                    rules: [
                        {
                            id: "19d5f544-cce0-4696-b1d1-aef1b1b272da",
                            criteria: {
                                eventType: {type: "Any"},
                                industry: null,
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments(individuals, teams, "team", {
                geography: false,
                roomCount: false,
                eventType: true,
                industry: true,
                eventNeeds: false,
                dayOfMonth: false
            });

            // They should conflict because:
            // - John has eventType=Any, industry=[Healthcare, Finance, Education]
            // - Joe inherits eventType=Social Event, industry=Any from team
            // - Both have same criteria types (eventType and industry)
            // - eventType: Any overlaps with Social Event
            // - industry: [Healthcare, Finance, Education] overlaps with Any
            expect(result.allOverlaps.length).toBeGreaterThanOrEqual(1);
            expect(result.entityIdsWithConflicts.has("1")).toBe(true);
            expect(result.entityIdsWithConflicts.has("4d3b7f01-c1bd-496f-989a-e07f6dd46f2b")).toBe(true);
        });

        it("incomplete rules should not overlap", () => {
            // Bob Dole's effective rule after team inheritance
            const bobEffectiveRule: AssignmentRule = {
                id: "bob-effective",
                criteria: {
                    eventType: {type: "Specific", values: ["Corporate", "Meeting"]}, // From team
                    industry: null, // Still null - team has no industry rule
                },
            };

            // John Smith's rule
            const johnRule: AssignmentRule = {
                id: "john",
                criteria: {
                    eventType: {type: "Specific", values: ["Meeting", "Conference"]},
                    industry: {type: "Specific", values: ["Technology", "Education"]},
                },
            };

            // They overlap on eventType (Meeting) but Bob has null industry
            // User's requirement: "this should not overlap since NEITHER the sales team NOR Bob dole have a rule set for Industry"
            // With the new logic, incomplete rules (those with null for active criteria) cannot overlap with any other rules
            const activeCriteria = {
                geography: false,
                roomCount: false,
                eventType: true,
                industry: true,
                eventNeeds: false,
                dayOfMonth: false
            };

            const result = doRulesOverlap(bobEffectiveRule, johnRule, activeCriteria);
            console.log("Bob effective rule:", JSON.stringify(bobEffectiveRule, null, 2));
            console.log("John rule:", JSON.stringify(johnRule, null, 2));
            console.log("Rules overlap?", result);
            expect(result).toBe(false);
        });

        it("should allow overlap when both rules are complete", () => {
            // Both rules have values for all active criteria - they should be able to overlap
            const rule1: AssignmentRule = {
                id: "rule1",
                criteria: {
                    eventType: {type: "Specific", values: ["Meeting", "Conference"]},
                    industry: {type: "Specific", values: ["Technology", "Education"]},
                },
            };

            const rule2: AssignmentRule = {
                id: "rule2",
                criteria: {
                    eventType: {type: "Specific", values: ["Meeting", "Wedding"]},
                    industry: {type: "Specific", values: ["Technology", "Healthcare"]},
                },
            };

            const activeCriteria = {
                geography: false,
                roomCount: false,
                eventType: true,
                industry: true,
                eventNeeds: false,
                dayOfMonth: false
            };

            // They should overlap because both have Meeting and Technology in common
            const result = doRulesOverlap(rule1, rule2, activeCriteria);
            expect(result).toBe(true);
        });

        it("should detect overlap when individual inherits complete rules from team - Screenshot 2 scenario", () => {
            // This test reproduces the exact scenario from Screenshot 2:
            // - John Smith: Event Type: Any, Industry: Any (standalone individual)
            // - Sales team: Event Type: Any, Industry: Any
            // - David Bones: inherits both Event Type and Industry from Sales team
            // Expected: David Bones and John Smith should conflict (both have Any/Any)

            const individuals: Individual[] = [
                {
                    id: "john-smith",
                    name: "John Smith",
                    title: "Sales Manager",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: null, // Standalone individual
                    rules: [{
                        id: "john-rule",
                        criteria: {
                            eventType: { type: "Any" },
                            industry: { type: "Any" }
                        }
                    }]
                },
                {
                    id: "david-bones",
                    name: "David Bones",
                    title: "Team Member",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: "sales-team",
                    rules: [{
                        id: "david-rule",
                        criteria: {
                            // David has no individual criteria - should inherit everything from team
                        }
                    }]
                }
            ];

            const teams: Team[] = [{
                id: "sales-team",
                name: "Sales",
                rules: [{
                    id: "sales-rule",
                    criteria: {
                        eventType: { type: "Any" },
                        industry: { type: "Any" }
                    }
                }]
            }];

            const activeCriteria = {
                geography: false,
                roomCount: false,
                eventType: true,
                industry: true,
                eventNeeds: false,
                dayOfMonth: false
            };

            const result = findOverlappingAssignments(individuals, teams, "team", activeCriteria);

            // Should detect conflict between John Smith and David Bones
            // Both should have effective rules with eventType: Any, industry: Any
            expect(result.allOverlaps.length).toBeGreaterThan(0);

            const johnDavidConflict = result.allOverlaps.find(overlap =>
                overlap.involvedEntityIds.includes("john-smith") &&
                overlap.involvedEntityIds.includes("david-bones")
            );

            expect(johnDavidConflict).toBeDefined();
            expect(johnDavidConflict?.type).toBe("conflict");
        });

        it("John Smith vs Bob Jones", () => {
            // John Smith: Conference, Wedding, +1 more | Retail, Manufacturing, +2 more | AV Equipment, Meeting Space
            const johnSmithRule: AssignmentRule = {
                id: "john-smith-rule",
                criteria: {
                    eventType: {type: "Specific", values: ["Conference", "Wedding"]},
                    industry: {type: "Specific", values: ["Retail", "Manufacturing"]},
                    eventNeeds: {type: "Specific", values: ["AV Equipment", "Meeting Space"]},
                },
            };

            // Bob Jones: Inherited from team (Any eventType) | Manufacturing, Education, +1 more | Not Set (null)
            // After inheritance from Sales team, Bob's effective rule would be:
            const bobJonesEffectiveRule: AssignmentRule = {
                id: "bob-jones-effective",
                criteria: {
                    eventType: {type: "Any"}, // Inherited from Sales team
                    industry: {type: "Specific", values: ["Manufacturing", "Education"]},
                    eventNeeds: null, // Not set and not inherited from team
                },
            };

            const activeCriteria = {
                geography: false,
                roomCount: false,
                eventType: true,
                industry: true,
                eventNeeds: true,
                dayOfMonth: false
            };

            // They should NOT overlap because Bob's rule is incomplete (eventNeeds is null)
            const result = doRulesOverlap(johnSmithRule, bobJonesEffectiveRule, activeCriteria);
            expect(result).toBe(false);
        });

        it("should handle undefined criteria - rules with different criteria don't overlap", () => {
            const rule1: AssignmentRule = {
                id: "rule1",
                criteria: {
                    geography: {type: "Specific", values: ["USA"]},
                    // eventType is undefined (not specified)
                },
            };

            const rule2: AssignmentRule = {
                id: "rule2",
                criteria: {
                    geography: {type: "Specific", values: ["USA"]},
                    eventType: {type: "Specific", values: ["Wedding"]},
                },
            };

            // They should NOT overlap because rule2 has eventType criteria that rule1 doesn't have
            // This aligns with the new behavior where rules must have the same set of criteria
            expect(doRulesOverlap(rule1, rule2)).toBe(false);
        });

        it("should handle empty specific values arrays", () => {
            const rule1: AssignmentRule = {
                id: "rule1",
                criteria: {
                    geography: {type: "Specific", values: []},
                    eventType: {type: "Specific", values: ["Wedding"]},
                },
            };

            const rule2: AssignmentRule = {
                id: "rule2",
                criteria: {
                    geography: {type: "Specific", values: ["USA"]},
                    eventType: {type: "Specific", values: ["Wedding"]},
                },
            };

            // They don't overlap because rule1 has empty geography values
            expect(doRulesOverlap(rule1, rule2)).toBe(false);
        });
    });

    describe("edge cases", () => {
        it("should handle rules where all criteria are null in one rule", () => {
            const rule1: AssignmentRule = {
                id: "rule1",
                criteria: {
                    geography: null,
                    eventType: null,
                    industry: null,
                },
            };

            const rule2: AssignmentRule = {
                id: "rule2",
                criteria: {
                    geography: {type: "Specific", values: ["USA"]},
                    eventType: {type: "Specific", values: ["Wedding"]},
                    industry: {type: "Specific", values: ["Tech"]},
                },
            };

            // They don't overlap because rule1 has no valid criteria to match
            expect(doRulesOverlap(rule1, rule2)).toBe(false);
        });
    });
});

describe("findOverlappingAssignments", () => {
    describe("conflict detection", () => {
        it("should detect conflicts between teams", () => {
            const teams: Team[] = [
                {
                    id: "team1",
                    name: "Sales Team A",
                    rules: [
                        {
                            id: "team1-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                                eventType: {type: "Specific", values: ["Wedding"]},
                            },
                        },
                    ],
                },
                {
                    id: "team2",
                    name: "Sales Team B",
                    rules: [
                        {
                            id: "team2-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA", "Canada"]},
                                eventType: {type: "Specific", values: ["Wedding", "Corporate"]},
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments([], teams, "team", {
                geography: true,
                roomCount: false,
                eventType: true,
                industry: false,
                eventNeeds: false,
                dayOfMonth: false
            });

            expect(result.allOverlaps).toHaveLength(1);
            expect(result.allOverlaps[0].type).toBe("conflict");
            expect(result.allOverlaps[0].involvedEntityIds).toEqual(["team1", "team2"]);
            expect(result.entityIdsWithConflicts.has("team1")).toBe(true);
            expect(result.entityIdsWithConflicts.has("team2")).toBe(true);
        });

        it("should detect conflicts between individuals in different teams", () => {
            const individuals: Individual[] = [
                {
                    id: "ind1",
                    name: "John Smith",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: "team1",
                    rules: [
                        {
                            id: "ind1-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                            },
                        },
                    ],
                },
                {
                    id: "ind2",
                    name: "Jane Doe",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: "team2",
                    rules: [
                        {
                            id: "ind2-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA", "Canada"]},
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments(individuals, [], "team");

            // In team mode, individuals from different teams can have conflicting rules
            expect(result.allOverlaps).toHaveLength(1);
            expect(result.allOverlaps[0].type).toBe("conflict");
            expect(result.allOverlaps[0].involvedEntityIds.sort()).toEqual(["ind1", "ind2"]);
        });
    });

    describe("redundancy detection", () => {
        it("should detect redundancies within the same team", () => {
            const teams: Team[] = [
                {
                    id: "team1",
                    name: "Sales Team A",
                    rules: [
                        {
                            id: "team1-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                                eventType: {type: "Any"},
                            },
                        },
                        {
                            id: "team1-rule2",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                                eventType: {type: "Specific", values: ["Wedding"]},
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments([], teams, "team");

            expect(result.allOverlaps).toHaveLength(1);
            expect(result.allOverlaps[0].type).toBe("redundancy");
            expect(result.allOverlaps[0].involvedEntityIds).toEqual(["team1"]);
            expect(result.entityIdsWithRedundanciesOnly.has("team1")).toBe(true);
            expect(result.entityIdsWithConflicts.has("team1")).toBe(false);
        });

        it("should detect redundancies within the same individual", () => {
            const individuals: Individual[] = [
                {
                    id: "ind1",
                    name: "John Smith",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    rules: [
                        {
                            id: "ind1-rule1",
                            criteria: {
                                geography: {type: "Any"},
                            },
                        },
                        {
                            id: "ind1-rule2",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments(individuals, [], "individual");

            expect(result.allOverlaps).toHaveLength(1);
            expect(result.allOverlaps[0].type).toBe("redundancy");
            expect(result.allOverlaps[0].summary).toContain("redundant rules");
        });
    });

    it("should not overlap when both have null industry but different eventType sets", () => {
        const individuals: Individual[] = [
            {
                id: "1",
                name: "John Smith",
                title: "Sales Manager",
                email: "<EMAIL>",
                phone: "************",
                teamId: null,
                rules: [{
                    id: "john-rule",
                    criteria: {
                        eventType: {type: "Any"},
                        industry: null
                    }
                }]
            },
            {
                id: "2",
                name: "Kim K",
                title: "Team Member",
                email: "<EMAIL>",
                phone: "************",
                teamId: "sales-team",
                rules: [{
                    id: "kim-rule",
                    criteria: {
                        eventType: {type: "Specific", values: ["Social Event", "Wedding"]},
                        industry: null
                    }
                }]
            }
        ];

        const teams: Team[] = [{
            id: "sales-team",
            name: "Sales",
            rules: [{
                id: "sales-rule",
                criteria: {
                    eventType: null,
                    industry: {type: "Any"}
                }
            }]
        }];

        const activeCriteria = {
            geography: false,
            roomCount: false,
            eventType: true,
            industry: true,
            eventNeeds: false,
            dayOfMonth: false
        };

        // In individual mode, they shouldn't overlap because both have null industry (active criterion)
        const resultIndividual = findOverlappingAssignments(individuals, teams, "individual", activeCriteria);
        expect(resultIndividual.allOverlaps).toHaveLength(0);

        // In team mode, Kim K inherits team's industry rule, but John Smith still has null industry
        // Since John Smith's rule is incomplete (null industry), they should NOT overlap
        const resultTeam = findOverlappingAssignments(individuals, teams, "team", activeCriteria);
        expect(resultTeam.allOverlaps).toHaveLength(0);
    });

    it("should handle team inheritance when individual has empty rules array", () => {
        // Based on user's debug output: Joe grant has no individual rules but is on sales team
        const individuals: Individual[] = [
            {
                id: "1",
                name: "John Smith",
                title: "Sales Manager",
                email: "<EMAIL>",
                phone: "************",
                teamId: null,
                rules: [{
                    id: "john-rule",
                    criteria: {
                        industry: {type: "Specific", values: ["Finance"]},
                        eventNeeds: {type: "Specific", values: ["Accommodations", "Meeting Space"]}
                    }
                }]
            },
            {
                id: "2",
                name: "Joe grant",
                title: "Team Member",
                email: "<EMAIL>",
                phone: "************",
                teamId: "sales-team",
                rules: [] // Empty rules array - should inherit team rules
            }
        ];

        const teams: Team[] = [{
            id: "sales-team",
            name: "sales",
            rules: [{
                id: "sales-rule",
                criteria: {
                    industry: {type: "Any"},
                    eventNeeds: {type: "Specific", values: ["Accommodations", "Meeting Space"]}
                }
            }]
        }];

        const activeCriteria = {
            geography: false,
            roomCount: false,
            eventType: false,
            industry: true,
            eventNeeds: true,
            dayOfMonth: false
        };

        // In team mode, Joe should inherit team rules and overlap with John
        // We expect 2 conflicts: sales team vs John Smith, and Joe grant vs John Smith
        const resultTeam = findOverlappingAssignments(individuals, teams, "team", activeCriteria);
        expect(resultTeam.allOverlaps).toHaveLength(2);

        // Should have conflict between John and Joe (with inherited rules)
        const joeJohnConflict = resultTeam.allOverlaps.find(o =>
            o.involvedEntityIds.includes("1") && o.involvedEntityIds.includes("2")
        );
        expect(joeJohnConflict).toBeDefined();
        expect(joeJohnConflict?.type).toBe("conflict");

        // Should have conflict between sales team and John
        const teamJohnConflict = resultTeam.allOverlaps.find(o =>
            o.involvedEntityIds.includes("1") && o.involvedEntityIds.includes("sales-team")
        );
        expect(teamJohnConflict).toBeDefined();
        expect(teamJohnConflict?.type).toBe("conflict");
    });

    describe("mixed scenarios", () => {
        it("should prioritize conflicts over redundancies for entity classification", () => {
            const teams: Team[] = [
                {
                    id: "team1",
                    name: "Sales Team A",
                    rules: [
                        {
                            id: "team1-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                            },
                        },
                        {
                            id: "team1-rule2",
                            criteria: {
                                geography: {type: "Specific", values: ["USA", "Canada"]},
                            },
                        },
                    ],
                },
                {
                    id: "team2",
                    name: "Sales Team B",
                    rules: [
                        {
                            id: "team2-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments([], teams, "team");

            // Should have 2 overlaps: 1 redundancy within team1, and 2 conflicts with team2
            expect(result.allOverlaps.length).toBeGreaterThanOrEqual(2);

            // Team1 should be in conflicts (not redundancies) because it has conflicts with team2
            expect(result.entityIdsWithConflicts.has("team1")).toBe(true);
            expect(result.entityIdsWithRedundanciesOnly.has("team1")).toBe(false);

            // Team2 should be in conflicts
            expect(result.entityIdsWithConflicts.has("team2")).toBe(true);
        });

        it("should handle individuals with no team assignment", () => {
            const individuals: Individual[] = [
                {
                    id: "ind1",
                    name: "John Smith",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: null,
                    rules: [
                        {
                            id: "ind1-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                            },
                        },
                    ],
                },
                {
                    id: "ind2",
                    name: "Jane Doe",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: null,
                    rules: [
                        {
                            id: "ind2-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments(individuals, [], "individual");

            expect(result.allOverlaps).toHaveLength(1);
            expect(result.allOverlaps[0].type).toBe("conflict");
        });
    });

    describe("individual assignment strategy", () => {
        it("should detect conflicts between individuals with same geography in individual mode", () => {
            const individuals: Individual[] = [
                {
                    id: "ind1",
                    name: "Emma Williams",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: null,
                    rules: [
                        {
                            id: "ind1-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["north-america"]},
                            },
                        },
                    ],
                },
                {
                    id: "ind2",
                    name: "James Anderson",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: null,
                    rules: [
                        {
                            id: "ind2-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["north-america"]},
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments(individuals, [], "individual");

            expect(result.allOverlaps).toHaveLength(1);
            expect(result.allOverlaps[0].type).toBe("conflict");
            expect(result.allOverlaps[0].involvedEntityIds.sort()).toEqual(["ind1", "ind2"]);
            expect(result.entityIdsWithConflicts.has("ind1")).toBe(true);
            expect(result.entityIdsWithConflicts.has("ind2")).toBe(true);
        });
    });

    describe("multiple rules per entity (v2)", () => {
        it("should detect conflicts between different rules of different individuals", () => {
            const individuals: Individual[] = [
                {
                    id: "ind1",
                    name: "John Smith",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    rules: [
                        {
                            id: "ind1-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                                eventType: {type: "Specific", values: ["Wedding"]},
                            },
                        },
                        {
                            id: "ind1-rule2",
                            criteria: {
                                geography: {type: "Specific", values: ["Canada"]},
                                eventType: {type: "Specific", values: ["Corporate"]},
                            },
                        },
                    ],
                },
                {
                    id: "ind2",
                    name: "Jane Doe",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    rules: [
                        {
                            id: "ind2-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                                eventType: {type: "Specific", values: ["Wedding", "Social"]},
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments(individuals, [], "individual");

            // Should find conflict between ind1-rule1 and ind2-rule1 (USA + Wedding)
            expect(result.allOverlaps).toHaveLength(1);
            expect(result.allOverlaps[0].type).toBe("conflict");
            expect(result.allOverlaps[0].overlappingRuleIds).toEqual(["ind1-rule1", "ind2-rule1"]);
            expect(result.entityIdsWithConflicts.has("ind1")).toBe(true);
            expect(result.entityIdsWithConflicts.has("ind2")).toBe(true);
        });

        it("should detect redundancies between multiple rules of the same individual", () => {
            const individuals: Individual[] = [
                {
                    id: "ind1",
                    name: "John Smith",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    rules: [
                        {
                            id: "ind1-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA", "Canada"]},
                                eventType: {type: "Any"},
                            },
                        },
                        {
                            id: "ind1-rule2",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                                eventType: {type: "Specific", values: ["Wedding"]},
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments(individuals, [], "individual");

            // Should find redundancy between ind1-rule1 and ind1-rule2
            expect(result.allOverlaps).toHaveLength(1);
            expect(result.allOverlaps[0].type).toBe("redundancy");
            expect(result.allOverlaps[0].involvedEntityIds).toEqual(["ind1"]);
            expect(result.allOverlaps[0].rule1Index).toBe(0);
            expect(result.allOverlaps[0].rule2Index).toBe(1);
            expect(result.entityIdsWithRedundanciesOnly.has("ind1")).toBe(true);
            expect(result.entityIdsWithConflicts.has("ind1")).toBe(false);
        });

        it("should handle individuals with multiple rules that create multiple overlaps", () => {
            const individuals: Individual[] = [
                {
                    id: "ind1",
                    name: "John Smith",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    rules: [
                        {
                            id: "ind1-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                            },
                        },
                        {
                            id: "ind1-rule2",
                            criteria: {
                                geography: {type: "Specific", values: ["Canada"]},
                            },
                        },
                    ],
                },
                {
                    id: "ind2",
                    name: "Jane Doe",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    rules: [
                        {
                            id: "ind2-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA", "Mexico"]},
                            },
                        },
                        {
                            id: "ind2-rule2",
                            criteria: {
                                geography: {type: "Specific", values: ["Canada", "Brazil"]},
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments(individuals, [], "individual");

            // Should find conflicts: ind1-rule1 with ind2-rule1 (USA), ind1-rule2 with ind2-rule2 (Canada)
            expect(result.allOverlaps).toHaveLength(2);
            expect(result.allOverlaps.every(o => o.type === "conflict")).toBe(true);
            expect(result.entityIdsWithConflicts.has("ind1")).toBe(true);
            expect(result.entityIdsWithConflicts.has("ind2")).toBe(true);
        });
    });

    describe("team rule inheritance and multiple rules", () => {
        it("should not create conflicts between team members with individual rules in team mode", () => {
            const individuals: Individual[] = [
                {
                    id: "ind1",
                    name: "John Smith",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: "team1",
                    rules: [
                        {
                            id: "ind1-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                            },
                        },
                    ],
                },
                {
                    id: "ind2",
                    name: "Jane Doe",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: "team1",
                    rules: [
                        {
                            id: "ind2-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                            },
                        },
                    ],
                },
            ];

            const teams: Team[] = [
                {
                    id: "team1",
                    name: "Sales Team A",
                    rules: [],
                },
            ];

            const result = findOverlappingAssignments(individuals, teams, "team");

            // In team mode, individuals from the same team should not conflict
            expect(result.allOverlaps).toHaveLength(0);
        });

        it("should not create conflicts between team and its own members in team mode", () => {
            const teams: Team[] = [
                {
                    id: "team1",
                    name: "Sales Team",
                    rules: [
                        {
                            id: "team1-rule1",
                            criteria: {
                                geography: {type: "Any"},
                                eventType: {type: "Specific", values: ["Wedding"]},
                            },
                        },
                    ],
                },
            ];

            const individuals: Individual[] = [
                {
                    id: "ind1",
                    name: "Jane Doe",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: "team1", // Member of team1
                    rules: [
                        {
                            id: "ind1-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["Canada"]},
                                eventType: {type: "Specific", values: ["Wedding"]},
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments(individuals, teams, "team");

            // Team member should not conflict with their own team's rules
            expect(result.allOverlaps).toHaveLength(0);
            expect(result.entityIdsWithConflicts.has("team1")).toBe(false);
            expect(result.entityIdsWithConflicts.has("ind1")).toBe(false);
        });

        it("should detect conflicts between teams with multiple rules", () => {
            const teams: Team[] = [
                {
                    id: "team1",
                    name: "Sales Team A",
                    rules: [
                        {
                            id: "team1-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                                eventType: {type: "Specific", values: ["Wedding"]},
                            },
                        },
                        {
                            id: "team1-rule2",
                            criteria: {
                                geography: {type: "Specific", values: ["Canada"]},
                                eventType: {type: "Any"},
                            },
                        },
                    ],
                },
                {
                    id: "team2",
                    name: "Sales Team B",
                    rules: [
                        {
                            id: "team2-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA", "Mexico"]},
                                eventType: {type: "Specific", values: ["Wedding", "Corporate"]},
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments([], teams, "team");

            // Should find conflict between team1-rule1 and team2-rule1 (USA + Wedding)
            expect(result.allOverlaps.length).toBeGreaterThanOrEqual(1);
            expect(result.allOverlaps.some(o =>
                o.type === "conflict" &&
                o.overlappingRuleIds.includes("team1-rule1") &&
                o.overlappingRuleIds.includes("team2-rule1")
            )).toBe(true);
            expect(result.entityIdsWithConflicts.has("team1")).toBe(true);
            expect(result.entityIdsWithConflicts.has("team2")).toBe(true);
        });

        it("should handle mixed scenarios with teams having multiple rules and redundancies", () => {
            const teams: Team[] = [
                {
                    id: "team1",
                    name: "Sales Team A",
                    rules: [
                        {
                            id: "team1-rule1",
                            criteria: {
                                geography: {type: "Any"},
                                eventType: {type: "Specific", values: ["Wedding"]},
                            },
                        },
                        {
                            id: "team1-rule2",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                                eventType: {type: "Specific", values: ["Wedding", "Corporate"]},
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments([], teams, "team");

            // Should find redundancy within team1 (rule1 overlaps with rule2 for USA + Wedding)
            expect(result.allOverlaps).toHaveLength(1);
            expect(result.allOverlaps[0].type).toBe("redundancy");
            expect(result.allOverlaps[0].involvedEntityIds).toEqual(["team1"]);
            expect(result.entityIdsWithRedundanciesOnly.has("team1")).toBe(true);
        });

        it("should properly track rule indices in overlap details", () => {
            const individuals: Individual[] = [
                {
                    id: "ind1",
                    name: "John Smith",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    rules: [
                        {
                            id: "skip1",
                            criteria: {
                                geography: {type: "Specific", values: ["Mexico"]},
                            },
                        },
                        {
                            id: "skip2",
                            criteria: {
                                geography: {type: "Specific", values: ["Brazil"]},
                            },
                        },
                        {
                            id: "ind1-overlap",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                            },
                        },
                    ],
                },
                {
                    id: "ind2",
                    name: "Jane Doe",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    rules: [
                        {
                            id: "ind2-overlap",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments(individuals, [], "individual");

            expect(result.allOverlaps).toHaveLength(1);
            expect(result.allOverlaps[0].rule1Index).toBe(2); // Third rule of ind1
            expect(result.allOverlaps[0].rule2Index).toBe(0); // First rule of ind2
            expect(result.allOverlaps[0].summary).toContain("Rule 3"); // Human-readable index
            expect(result.allOverlaps[0].summary).toContain("Rule 1");
        });
    });

    describe("team rule inheritance in conflict detection", () => {
        it("should not detect conflicts when team has no rule for a criterion", () => {
            const teams: Team[] = [
                {
                    id: "sales-team",
                    name: "Sales",
                    rules: [
                        {
                            id: "sales-rule",
                            criteria: {
                                eventType: {type: "Specific", values: ["Corporate", "Meeting"]},
                                // NO industry rule at team level
                            },
                        },
                    ],
                },
            ];

            const individuals: Individual[] = [
                {
                    id: "john-smith",
                    name: "John Smith",
                    title: "Sales Manager",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: null, // Standalone
                    rules: [
                        {
                            id: "john-rule",
                            criteria: {
                                eventType: {type: "Specific", values: ["Meeting", "Conference"]},
                                industry: {type: "Specific", values: ["Technology", "Education"]},
                            },
                        },
                    ],
                },
                {
                    id: "bob-dole",
                    name: "Bob Dole",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: "sales-team", // Team member
                    rules: [
                        {
                            id: "bob-rule",
                            criteria: {
                                eventType: {type: "Any"}, // Will be overridden by team's specific values
                                industry: null, // No individual rule, and team has no rule either
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments(individuals, teams, "team");

            // Bob inherits team's eventType (Corporate, Meeting) which overlaps with John's (Meeting)
            // But Bob has no industry rule (null) and team has no industry rule
            // So they should NOT conflict
            console.log("Overlaps found:", JSON.stringify(result.allOverlaps, null, 2));
            expect(result.allOverlaps).toHaveLength(0);
            expect(result.entityIdsWithConflicts.has("john-smith")).toBe(false);
            expect(result.entityIdsWithConflicts.has("bob-dole")).toBe(false);
        });

        it("should handle null criteria correctly when team has partial rules", () => {
            // Updated test to align with new behavior: rules with different criteria sets don't overlap
            const teams: Team[] = [
                {
                    id: "sales-team",
                    name: "Sales",
                    rules: [
                        {
                            id: "sales-rule",
                            criteria: {
                                eventType: {type: "Specific", values: ["Corporate", "Meeting"]},
                                // eventNeeds is not set at team level
                            },
                        },
                    ],
                },
            ];

            const individuals: Individual[] = [
                {
                    id: "john-smith",
                    name: "John Smith",
                    title: "Sales Manager",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: null, // Standalone
                    rules: [
                        {
                            id: "john-rule",
                            criteria: {
                                eventType: {type: "Specific", values: ["Meeting", "Conference", "Wedding"]},
                                eventNeeds: {type: "Any"},
                            },
                        },
                    ],
                },
                {
                    id: "joe-blow",
                    name: "Joe Blow",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: "sales-team", // Team member
                    rules: [
                        {
                            id: "joe-rule",
                            criteria: {
                                eventType: {type: "Any"}, // Will be overridden by team's specific values
                                eventNeeds: null, // No team rule for this, stays null
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments(individuals, teams, "team");

            // Joe inherits team's eventType (Corporate, Meeting) which overlaps with John's (Meeting)
            // But John has eventNeeds and Joe doesn't, so they should NOT overlap
            // This aligns with the new behavior where rules must have the same set of criteria
            expect(result.allOverlaps).toHaveLength(0);
            expect(result.entityIdsWithConflicts.has("joe-blow")).toBe(false);
            expect(result.entityIdsWithConflicts.has("john-smith")).toBe(false);
        });

        it("should detect conflicts when team members inherit team rules that conflict with standalone individuals", () => {
            const teams: Team[] = [
                {
                    id: "sales-team",
                    name: "Sales",
                    rules: [
                        {
                            id: "team-rule-1",
                            criteria: {
                                eventType: {type: "Any"},
                                industry: {type: "Specific", values: ["Finance", "Technology"]}, // Overlaps with John
                            },
                        },
                    ],
                },
            ];

            const individuals: Individual[] = [
                {
                    id: "john-smith",
                    name: "John Smith",
                    title: "Sales Manager",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: null, // Standalone
                    rules: [
                        {
                            id: "john-rule-1",
                            criteria: {
                                eventType: {type: "Any"},
                                industry: {type: "Specific", values: ["Technology", "Education"]},
                            },
                        },
                    ],
                },
                {
                    id: "bob-jones",
                    name: "Bob Jones",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: "sales-team", // Team member
                    rules: [
                        {
                            id: "bob-rule-1",
                            criteria: {
                                eventType: {type: "Any"},
                                industry: null, // Should be overridden by team's industry
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments(individuals, teams, "team");

            // Should find conflict between Bob Jones (with team's industry) and John Smith
            expect(result.allOverlaps.length).toBeGreaterThanOrEqual(1);
            const conflict = result.allOverlaps.find(o =>
                o.type === "conflict" &&
                o.involvedEntityIds.includes("bob-jones") &&
                o.involvedEntityIds.includes("john-smith")
            );
            expect(conflict).toBeDefined();
            expect(result.entityIdsWithConflicts.has("bob-jones")).toBe(true);
            expect(result.entityIdsWithConflicts.has("john-smith")).toBe(true);
        });
    });

    describe("team vs standalone individual conflicts", () => {
        it("should detect conflicts between teams and standalone individuals in team mode", () => {
            const teams: Team[] = [
                {
                    id: "team1",
                    name: "Sales Team",
                    rules: [
                        {
                            id: "team1-rule1",
                            criteria: {
                                geography: {type: "Any"},
                                eventType: {type: "Specific", values: ["Wedding"]},
                            },
                        },
                    ],
                },
            ];

            const individuals: Individual[] = [
                {
                    id: "ind1",
                    name: "John Smith",
                    title: "Sales Manager",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: null, // Standalone individual
                    rules: [
                        {
                            id: "ind1-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                                eventType: {type: "Specific", values: ["Wedding", "Corporate"]},
                            },
                        },
                    ],
                },
                {
                    id: "ind2",
                    name: "Jane Doe",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: "team1", // Team member - should not conflict with team
                    rules: [
                        {
                            id: "ind2-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["Canada"]},
                                eventType: {type: "Specific", values: ["Wedding"]},
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments(individuals, teams, "team");

            // Should find conflicts:
            // 1. team1 vs ind1 (standalone)
            // 2. ind2 (with inherited team rules) vs ind1
            expect(result.allOverlaps).toHaveLength(2);

            // Check for team vs standalone conflict
            const teamConflict = result.allOverlaps.find(o =>
                o.involvedEntityIds.includes("team1") && o.involvedEntityIds.includes("ind1")
            );
            expect(teamConflict).toBeDefined();
            expect(teamConflict?.type).toBe("conflict");

            // Check for team member (with inherited rules) vs standalone conflict
            const memberConflict = result.allOverlaps.find(o =>
                o.involvedEntityIds.includes("ind2") && o.involvedEntityIds.includes("ind1")
            );
            expect(memberConflict).toBeDefined();
            expect(memberConflict?.type).toBe("conflict");

            expect(result.entityIdsWithConflicts.has("team1")).toBe(true);
            expect(result.entityIdsWithConflicts.has("ind1")).toBe(true);
            expect(result.entityIdsWithConflicts.has("ind2")).toBe(true);
        });

        it("should handle multiple standalone individuals conflicting with teams", () => {
            const teams: Team[] = [
                {
                    id: "team1",
                    name: "Sales Team A",
                    rules: [
                        {
                            id: "team1-rule1",
                            criteria: {
                                industry: {type: "Specific", values: ["Finance", "Education"]},
                            },
                        },
                    ],
                },
            ];

            const individuals: Individual[] = [
                {
                    id: "ind1",
                    name: "Alice",
                    title: "Sales Manager",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: null,
                    rules: [
                        {
                            id: "ind1-rule1",
                            criteria: {
                                industry: {type: "Specific", values: ["Finance"]},
                            },
                        },
                    ],
                },
                {
                    id: "ind2",
                    name: "Bob",
                    title: "Sales Manager",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: null,
                    rules: [
                        {
                            id: "ind2-rule1",
                            criteria: {
                                industry: {type: "Specific", values: ["Education", "Healthcare"]},
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments(individuals, teams, "team");

            // Should find conflicts for both standalone individuals
            expect(result.allOverlaps).toHaveLength(2);
            expect(result.allOverlaps.every(o => o.type === "conflict")).toBe(true);
            expect(result.entityIdsWithConflicts.has("team1")).toBe(true);
            expect(result.entityIdsWithConflicts.has("ind1")).toBe(true);
            expect(result.entityIdsWithConflicts.has("ind2")).toBe(true);
        });

        it("should detect conflicts between team rules and standalone individuals", () => {
            const teams: Team[] = [
                {
                    id: "sales-team",
                    name: "sales",
                    rules: [
                        {
                            id: "team-rule-1",
                            criteria: {
                                eventType: {type: "Any"},
                                industry: {type: "Specific", values: ["Finance", "Education"]},
                            },
                        },
                    ],
                },
            ];

            const individuals: Individual[] = [
                {
                    id: "john-smith",
                    name: "John Smith",
                    title: "Sales Manager",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: null, // Standalone
                    rules: [
                        {
                            id: "john-rule-1",
                            criteria: {
                                eventType: {type: "Any"},
                                industry: {type: "Specific", values: ["Finance"]},
                            },
                        },
                    ],
                },
                {
                    id: "joe-bob",
                    name: "joe bob",
                    title: "Sales Rep",
                    email: "<EMAIL>",
                    phone: "************",
                    teamId: "sales-team", // Team member
                    rules: [], // No individual rules, inherits team rules
                },
            ];

            const result = findOverlappingAssignments(individuals, teams, "team");

            // Should find conflict between team and John Smith
            expect(result.allOverlaps.length).toBeGreaterThanOrEqual(1);
            const conflict = result.allOverlaps.find(o =>
                o.type === "conflict" &&
                o.involvedEntityIds.includes("sales-team") &&
                o.involvedEntityIds.includes("john-smith")
            );
            expect(conflict).toBeDefined();
            expect(result.entityIdsWithConflicts.has("sales-team")).toBe(true);
            expect(result.entityIdsWithConflicts.has("john-smith")).toBe(true);

            // joe bob should be in conflicts because he inherits team rules that conflict with John Smith
            expect(result.entityIdsWithConflicts.has("joe-bob")).toBe(true);
        });
    });

    describe("no overlap scenarios", () => {
        it("should return empty results when no overlaps exist", () => {
            const teams: Team[] = [
                {
                    id: "team1",
                    name: "Sales Team A",
                    rules: [
                        {
                            id: "team1-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                            },
                        },
                    ],
                },
                {
                    id: "team2",
                    name: "Sales Team B",
                    rules: [
                        {
                            id: "team2-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["Canada"]},
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments([], teams, "team");

            expect(result.allOverlaps).toHaveLength(0);
            expect(result.entityIdsWithConflicts.size).toBe(0);
            expect(result.entityIdsWithRedundanciesOnly.size).toBe(0);
        });

        it("should handle entities with no rules", () => {
            const teams: Team[] = [
                {
                    id: "team1",
                    name: "Sales Team A",
                    rules: [],
                },
                {
                    id: "team2",
                    name: "Sales Team B",
                    rules: [
                        {
                            id: "team2-rule1",
                            criteria: {
                                geography: {type: "Specific", values: ["USA"]},
                            },
                        },
                    ],
                },
            ];

            const result = findOverlappingAssignments([], teams, "team");

            expect(result.allOverlaps).toHaveLength(0);
        });
    });
});
