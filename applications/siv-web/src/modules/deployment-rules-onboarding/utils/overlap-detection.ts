import type {
  AssignmentRule,
  CriterionValue,
  CriteriaTypeString,
  GeographyRegion,
  Individual,
  RoomCountRange,
  Team,
} from "../types/lead-assignment";

/**
 * Simplified version of doRulesOverlap that works with AssignmentRule objects
 * without any exception logic
 * 
 * @param rule1 First rule to compare
 * @param rule2 Second rule to compare
 * @returns true if the rules overlap, false otherwise
 */
export const doRulesOverlap = (
  rule1: AssignmentRule,
  rule2: AssignmentRule,
  activeCriteria?: Record<string, boolean>
): boolean => {
  const criteriaTypes = new Set([
    ...Object.keys(rule1.criteria),
    ...Object.keys(rule2.criteria),
  ]) as Set<CriteriaTypeString>;

  // Track if we found at least one valid criterion to compare
  let hasValidCriterion = false;
  
  // Track which criteria are set (non-null) in each rule
  const rule1SetCriteria = new Set<CriteriaTypeString>();
  const rule2SetCriteria = new Set<CriteriaTypeString>();

  // First pass: identify which criteria are set in each rule
  for (const criteriaType of criteriaTypes) {
    const criterion1 = rule1.criteria[criteriaType];
    const criterion2 = rule2.criteria[criteriaType];

    // Special handling for active criteria with null values
    // If a criterion is active and BOTH rules have null for it, they cannot overlap
    // This prevents incomplete entities from overlapping with each other
    if (activeCriteria && activeCriteria[criteriaType]) {
      if (criterion1 === null && criterion2 === null) {
        return false;
      }
    }

    if (criterion1 !== undefined && criterion1 !== null) {
      rule1SetCriteria.add(criteriaType);
    }
    if (criterion2 !== undefined && criterion2 !== null) {
      rule2SetCriteria.add(criteriaType);
    }
  }

  // Check if one rule has criteria that the other doesn't
  // This is the key insight from the user's feedback
  // BUT: only apply this when both criteria are non-null for active criteria
  for (const criteriaType of rule1SetCriteria) {
    if (!rule2SetCriteria.has(criteriaType)) {
      // Rule1 has a criterion that Rule2 doesn't have
      // But if this is an active criterion and Rule2 has null, allow overlap
      const criterion2 = rule2.criteria[criteriaType];
      if (activeCriteria && activeCriteria[criteriaType] && criterion2 === null) {
        // Allow this - Rule2 has null for active criterion, Rule1 has value
        continue;
      }
      return false;
    }
  }
  for (const criteriaType of rule2SetCriteria) {
    if (!rule1SetCriteria.has(criteriaType)) {
      // Rule2 has a criterion that Rule1 doesn't have
      // But if this is an active criterion and Rule1 has null, allow overlap
      const criterion1 = rule1.criteria[criteriaType];
      if (activeCriteria && activeCriteria[criteriaType] && criterion1 === null) {
        // Allow this - Rule1 has null for active criterion, Rule2 has value
        continue;
      }
      return false;
    }
  }

  // If both rules have no valid criteria to compare, they don't overlap
  if (rule1SetCriteria.size === 0 && rule2SetCriteria.size === 0) {
    return false;
  }

  // For each criteria type, check if the rules overlap
  for (const criteriaType of criteriaTypes) {
    const criterion1 = rule1.criteria[criteriaType];
    const criterion2 = rule2.criteria[criteriaType];

    // If either rule doesn't specify this criteria, skip it
    // (undefined means not set, which is different from null or "Any")
    if (criterion1 === undefined || criterion2 === undefined) {
      continue;
    }

    // Handle null values - null means "not set" so skip this criterion
    if (criterion1 === null || criterion2 === null) {
      continue;
    }

    // At this point we have two non-null criteria to compare
    hasValidCriterion = true;

    // If either is "Any", they overlap on this criteria
    if (criterion1.type === "Any" || criterion2.type === "Any") {
      continue;
    }

    // Both are "Specific" - check if they have any common values
    const hasCommonValues = criterion1.values.some((value) =>
      criterion2.values.includes(value)
    );

    // If no common values for this criteria type, rules don't overlap
    if (!hasCommonValues) {
      return false;
    }
  }

  // If we never found any valid criteria to compare, rules don't overlap
  if (!hasValidCriterion) {
    return false;
  }

  // If we made it through all criteria types without finding a mismatch,
  // the rules overlap
  return true;
};

export type OverlapType = "conflict" | "redundancy";

export interface OverlapDetail {
  type: OverlapType;
  involvedEntityIds: string[];
  overlappingRuleIds: [string, string];
  entity1Name: string;
  entity2Name: string;
  rule1Index?: number;
  rule2Index?: number;
  summary: string;
}

/**
 * Find all overlapping assignments among individuals and teams
 * Distinguishes between conflicts (inter-entity) and redundancies (intra-entity)
 * 
 * @param individuals Array of individuals with rules
 * @param teams Array of teams with rules
 * @param assignmentStrategy Current assignment strategy
 * @returns Array of overlap details
 */
export const findOverlappingAssignments = (
  individuals: Individual[],
  teams: Team[],
  assignmentStrategy: "team" | "individual",
  activeCriteria?: Record<string, boolean>
): {
  allOverlaps: OverlapDetail[];
  entityIdsWithConflicts: Set<string>;
  entityIdsWithRedundanciesOnly: Set<string>;
} => {
  const allOverlaps: OverlapDetail[] = [];
  const entityIdsWithConflicts = new Set<string>();
  const entityIdsWithRedundanciesOnly = new Set<string>();

  // Helper to create a flattened list of all rules with their entity info
  interface RuleWithEntity {
    rule: AssignmentRule;
    entityId: string;
    entityName: string;
    entityType: "team" | "individual";
    ruleIndex: number;
  }

  const allRules: RuleWithEntity[] = [];

  // Add team rules
  if (assignmentStrategy === "team") {
    for (const team of teams) {
      if (!team.rules) continue;
      team.rules.forEach((rule, index) => {
        allRules.push({
          rule,
          entityId: team.id,
          entityName: team.name,
          entityType: "team",
          ruleIndex: index,
        });
      });
    }
  }

  // Add individual rules
  for (const individual of individuals) {
    // In team mode, if individual has no rules but belongs to a team, create rules from team
    if (assignmentStrategy === "team" && individual.teamId && (!individual.rules || individual.rules.length === 0)) {
      const team = teams.find(t => t.id === individual.teamId);
      if (team && team.rules && team.rules.length > 0) {
        // Create individual rules from team rules
        team.rules.forEach((teamRule, index) => {
          allRules.push({
            rule: teamRule, // Use team rule directly since individual has no rules
            entityId: individual.id,
            entityName: individual.name,
            entityType: "individual",
            ruleIndex: index,
          });
        });
      }
      continue; // Skip the normal rule processing for this individual
    }

    if (!individual.rules) continue;
    
    individual.rules.forEach((rule, index) => {
      // In team mode, if individual belongs to a team, apply team rule overrides
      let effectiveRule = rule;
      
      if (assignmentStrategy === "team" && individual.teamId) {
        const team = teams.find(t => t.id === individual.teamId);
        if (team && team.rules && team.rules.length > 0) {
          // Create a merged rule where team values override individual values for each criterion
          const mergedCriteria = { ...rule.criteria };
          
          // For each team rule, apply overrides
          team.rules.forEach(teamRule => {
            Object.keys(teamRule.criteria).forEach(criteriaType => {
              const teamValue = teamRule.criteria[criteriaType as CriteriaTypeString];
              
              // Team value overrides individual value if team has a value for this criterion
              if (teamValue !== null && teamValue !== undefined) {
                mergedCriteria[criteriaType as CriteriaTypeString] = teamValue;
              }
            });
          });
          
          effectiveRule = {
            ...rule,
            criteria: mergedCriteria,
          };
          
        }
      }
      
      allRules.push({
        rule: effectiveRule,
        entityId: individual.id,
        entityName: individual.name,
        entityType: "individual",
        ruleIndex: index,
      });
    });
  }

  // Debug: log all rules (commented out for production)
  // console.log("All rules for overlap detection:", allRules.map(r => ({
  //   entityName: r.entityName,
  //   entityType: r.entityType,
  //   rule: r.rule
  // })));

  // Compare all rules pairwise
  for (let i = 0; i < allRules.length; i++) {
    for (let j = i + 1; j < allRules.length; j++) {
      const ruleWithEntity1 = allRules[i];
      const ruleWithEntity2 = allRules[j];

      // Skip if rules don't overlap
      if (!doRulesOverlap(ruleWithEntity1.rule, ruleWithEntity2.rule, activeCriteria)) {
        continue;
      }

      // In team mode, skip conflicts between a team and its own members
      if (assignmentStrategy === "team") {
        const isTeamVsOwnMember = 
          (ruleWithEntity1.entityType === "team" && ruleWithEntity2.entityType === "individual" && 
           individuals.find(ind => ind.id === ruleWithEntity2.entityId)?.teamId === ruleWithEntity1.entityId) ||
          (ruleWithEntity2.entityType === "team" && ruleWithEntity1.entityType === "individual" && 
           individuals.find(ind => ind.id === ruleWithEntity1.entityId)?.teamId === ruleWithEntity2.entityId);
        
        if (isTeamVsOwnMember) {
          continue;
        }
      }

      // Determine if this is a conflict or redundancy
      const isSameEntity = ruleWithEntity1.entityId === ruleWithEntity2.entityId;
      const overlapType: OverlapType = isSameEntity ? "redundancy" : "conflict";

      // For team assignment strategy, individuals from different teams create conflicts
      if (
        assignmentStrategy === "team" &&
        ruleWithEntity1.entityType === "individual" &&
        ruleWithEntity2.entityType === "individual"
      ) {
        const individual1 = individuals.find(
          (ind) => ind.id === ruleWithEntity1.entityId
        );
        const individual2 = individuals.find(
          (ind) => ind.id === ruleWithEntity2.entityId
        );
        
        // Only create overlap if they're from different teams
        if (individual1?.teamId !== individual2?.teamId) {
          const overlap: OverlapDetail = {
            type: "conflict",
            involvedEntityIds: [ruleWithEntity1.entityId, ruleWithEntity2.entityId],
            overlappingRuleIds: [ruleWithEntity1.rule.id, ruleWithEntity2.rule.id],
            entity1Name: ruleWithEntity1.entityName,
            entity2Name: ruleWithEntity2.entityName,
            rule1Index: ruleWithEntity1.ruleIndex,
            rule2Index: ruleWithEntity2.ruleIndex,
            summary: `${ruleWithEntity1.entityName} (Rule ${ruleWithEntity1.ruleIndex + 1}) conflicts with ${ruleWithEntity2.entityName} (Rule ${ruleWithEntity2.ruleIndex + 1})`,
          };
          allOverlaps.push(overlap);
          entityIdsWithConflicts.add(ruleWithEntity1.entityId);
          entityIdsWithConflicts.add(ruleWithEntity2.entityId);
        }
      } else {
        // Standard overlap handling
        const overlap: OverlapDetail = {
          type: overlapType,
          involvedEntityIds: isSameEntity
            ? [ruleWithEntity1.entityId]
            : [ruleWithEntity1.entityId, ruleWithEntity2.entityId],
          overlappingRuleIds: [ruleWithEntity1.rule.id, ruleWithEntity2.rule.id],
          entity1Name: ruleWithEntity1.entityName,
          entity2Name: ruleWithEntity2.entityName,
          rule1Index: ruleWithEntity1.ruleIndex,
          rule2Index: ruleWithEntity2.ruleIndex,
          summary: isSameEntity
            ? `${ruleWithEntity1.entityName} has redundant rules: Rule ${ruleWithEntity1.ruleIndex + 1} and Rule ${ruleWithEntity2.ruleIndex + 1}`
            : `${ruleWithEntity1.entityName} (Rule ${ruleWithEntity1.ruleIndex + 1}) conflicts with ${ruleWithEntity2.entityName} (Rule ${ruleWithEntity2.ruleIndex + 1})`,
        };
        allOverlaps.push(overlap);

        if (overlapType === "conflict") {
          entityIdsWithConflicts.add(ruleWithEntity1.entityId);
          entityIdsWithConflicts.add(ruleWithEntity2.entityId);
          // Remove from redundancies if they were there
          entityIdsWithRedundanciesOnly.delete(ruleWithEntity1.entityId);
          entityIdsWithRedundanciesOnly.delete(ruleWithEntity2.entityId);
        } else {
          // Only add to redundancies if not already in conflicts
          if (!entityIdsWithConflicts.has(ruleWithEntity1.entityId)) {
            entityIdsWithRedundanciesOnly.add(ruleWithEntity1.entityId);
          }
        }
      }
    }
  }

  return {
    allOverlaps,
    entityIdsWithConflicts,
    entityIdsWithRedundanciesOnly,
  };
};