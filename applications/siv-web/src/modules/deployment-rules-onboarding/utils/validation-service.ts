/**
 * Validation utilities for Lead Assignment Configuration v2
 * 
 * This version works with the new multiple rules per entity model
 * and distinguishes between conflicts and redundancies
 */

import type {
  Individual,
  Team,
  CoverageGap,
  AssignmentStrategy,
  GeographyRegion,
  RoomCountRange,
  CriteriaTypeString,
} from "../types/lead-assignment";

import {
  findOverlappingAssignments,
  type OverlapDetail,
} from "./overlap-detection";

import { detectCoverageGaps } from "./coverage-gap-detection";

/**
 * Configuration for validation
 */
export interface ValidationConfig {
  individuals: Individual[];
  teams: Team[];
  activeCriteria: Record<CriteriaTypeString, boolean>;
  assignmentStrategy: AssignmentStrategy;
  geographyRegions: GeographyRegion[];
  roomCountRanges: RoomCountRange[];
  gapsAcknowledged?: boolean;
}

/**
 * Represents a rule with unset criteria
 */
export interface RuleWithUnsetCriteria {
  entityId: string;
  entityName: string;
  entityType: "individual" | "team";
  ruleId: string;
  unsetCriteria: CriteriaTypeString[];
}

/**
 * Comprehensive validation result with conflict/redundancy distinction
 */
export interface ValidationResult {
  // Overall validation status
  isValid: boolean;

  // Specific issue types
  hasConflicts: boolean;
  hasRedundancies: boolean;
  hasCoverageGaps: boolean;
  hasInactiveEntities: boolean;
  hasUnsetCriteria: boolean;

  // Detailed results with new overlap structure
  allOverlaps: OverlapDetail[];
  entityIdsWithConflicts: Set<string>;
  entityIdsWithRedundanciesOnly: Set<string>;
  coverageGaps: CoverageGap[];
  inactiveEntityIds: string[]; // Entities with no rules
  rulesWithUnsetCriteria: RuleWithUnsetCriteria[]; // Rules with null criteria

  // Additional metadata
  timestamp: number;
}

/**
 * Predefined criteria options for different types
 */
const PREDEFINED_CRITERIA_OPTIONS = {
  eventType: [
    "Meeting",
    "Conference",
    "Wedding",
    "Social Event",
    "Corporate Event",
  ],
  industry: [
    "Government",
    "Technology",
    "Healthcare",
    "Finance",
    "Education",
    "Manufacturing",
    "Retail",
    "Other",
  ],
  eventNeeds: [
    "Catering",
    "AV Equipment",
    "Meeting Space",
    "Accommodations",
    "Transportation",
  ],
} as const;

/**
 * Find entities that have no rules defined (inactive)
 */
function findInactiveEntities(
  individuals: Individual[],
  teams: Team[]
): string[] {
  const inactiveEntityIds: string[] = [];

  // Check teams
  for (const team of teams) {
    if (!team.rules || team.rules.length === 0) {
      inactiveEntityIds.push(team.id);
    }
  }

  // Check individuals
  for (const individual of individuals) {
    if (!individual.rules || individual.rules.length === 0) {
      inactiveEntityIds.push(individual.id);
    }
  }

  return inactiveEntityIds;
}

/**
 * Find rules that have unset (null) criteria for active criteria types
 */
function findRulesWithUnsetCriteria(
  individuals: Individual[],
  teams: Team[],
  activeCriteria: Record<CriteriaTypeString, boolean>
): RuleWithUnsetCriteria[] {
  const rulesWithUnsetCriteria: RuleWithUnsetCriteria[] = [];

  // Helper to check a single entity's rules
  const checkEntityRules = (
    entity: Individual | Team,
    entityType: "individual" | "team"
  ) => {
    if (!entity.rules || entity.rules.length === 0) {
      return; // Skip entities with no rules
    }

    for (const rule of entity.rules) {
      const unsetCriteria: CriteriaTypeString[] = [];

      // Check each active criteria type
      for (const [criteriaType, isActive] of Object.entries(activeCriteria)) {
        if (isActive) {
          const criteriaValue = rule.criteria[criteriaType as CriteriaTypeString];
          
          // Check if criteria is null (unset)
          if (criteriaValue === null) {
            unsetCriteria.push(criteriaType as CriteriaTypeString);
          }
          // Note: undefined means this criteria is not specified in this rule, which is OK
        }
      }

      if (unsetCriteria.length > 0) {
        rulesWithUnsetCriteria.push({
          entityId: entity.id,
          entityName: entity.name,
          entityType,
          ruleId: rule.id,
          unsetCriteria,
        });
      }
    }
  };

  // Check all teams
  for (const team of teams) {
    checkEntityRules(team, "team");
  }

  // Check all individuals
  for (const individual of individuals) {
    checkEntityRules(individual, "individual");
  }

  return rulesWithUnsetCriteria;
}

/**
 * Builds a map of all possible values for each criteria type
 */
function buildAllPossibleValuesMap(
  individuals: Individual[],
  teams: Team[],
  activeCriteria: Record<CriteriaTypeString, boolean>,
  geographyRegions: GeographyRegion[],
  roomCountRanges: RoomCountRange[]
): Partial<Record<CriteriaTypeString, Set<string>>> {
  const allPossibleValues: Partial<Record<CriteriaTypeString, Set<string>>> = {};

  // Populate with registry values for geography
  if (activeCriteria.geography) {
    allPossibleValues.geography = new Set(
      geographyRegions.map((region) => region.id)
    );
  }

  // Populate with registry values for roomCount
  if (activeCriteria.roomCount) {
    allPossibleValues.roomCount = new Set(
      roomCountRanges.map((range) => range.id)
    );
  }

  // For other criteria types, use predefined options
  if (activeCriteria.eventType) {
    allPossibleValues.eventType = new Set(PREDEFINED_CRITERIA_OPTIONS.eventType);
  }

  if (activeCriteria.industry) {
    allPossibleValues.industry = new Set(PREDEFINED_CRITERIA_OPTIONS.industry);
  }

  if (activeCriteria.eventNeeds) {
    allPossibleValues.eventNeeds = new Set(PREDEFINED_CRITERIA_OPTIONS.eventNeeds);
  }

  // Special handling for dayOfMonth
  if (activeCriteria.dayOfMonth) {
    allPossibleValues.dayOfMonth = new Set(["Odd Days", "Even Days"]);
  }

  return allPossibleValues;
}

/**
 * Enriches a coverage gap with human-readable display values
 */
function enrichGapWithDisplayValues(
  gap: CoverageGap,
  geographyRegions: GeographyRegion[],
  roomCountRanges: RoomCountRange[]
): void {
  const parts = [];

  for (const criteria of gap.missingCombination) {
    const { criteriaType, value } = criteria;
    let displayValue = value;

    // Add human-readable display values for IDs
    if (criteriaType === "geography" && geographyRegions.length > 0) {
      const region = geographyRegions.find((r) => r.id === value);
      if (region) displayValue = region.name;
    } else if (criteriaType === "roomCount" && roomCountRanges.length > 0) {
      const range = roomCountRanges.find((r) => r.id === value);
      if (range) displayValue = range.name;
    }

    // Update the display value in the gap
    criteria.displayValue = displayValue;

    // Add to the description parts
    const displayCriteriaType =
      criteriaType === "dayOfMonth"
        ? "Day of Month"
        : criteriaType.charAt(0).toUpperCase() + criteriaType.slice(1);

    parts.push(`${displayCriteriaType}: ${displayValue}`);
  }

  // Build the full description
  gap.description = parts.join(", ");
}

/**
 * Main validation function
 */
export function validateAssignmentConfiguration(
  config: ValidationConfig
): ValidationResult {
  const {
    individuals,
    teams,
    activeCriteria,
    assignmentStrategy,
    geographyRegions,
    roomCountRanges,
    gapsAcknowledged = false,
  } = config;

  // Find inactive entities (those with no rules)
  const inactiveEntityIds = findInactiveEntities(individuals, teams);

  // Find rules with unset criteria
  const rulesWithUnsetCriteria = findRulesWithUnsetCriteria(
    individuals,
    teams,
    activeCriteria
  );

  // Find overlapping assignments (conflicts and redundancies)
  const overlapResults = findOverlappingAssignments(
    individuals,
    teams,
    assignmentStrategy,
    activeCriteria
  );

  // Build possible values map for coverage gap detection
  const allPossibleValues = buildAllPossibleValuesMap(
    individuals,
    teams,
    activeCriteria,
    geographyRegions,
    roomCountRanges
  );

  // Detect coverage gaps
  const rawGaps = detectCoverageGaps(
    individuals,
    teams,
    activeCriteria,
    allPossibleValues,
    geographyRegions
  );

  // Enrich gaps with display values
  const coverageGaps = rawGaps.map((gap) => {
    enrichGapWithDisplayValues(gap, geographyRegions, roomCountRanges);
    return gap;
  });

  // Determine overall validity
  const hasConflicts = overlapResults.entityIdsWithConflicts.size > 0;
  const hasRedundancies = overlapResults.entityIdsWithRedundanciesOnly.size > 0;
  const hasCoverageGaps = coverageGaps.length > 0;
  const hasInactiveEntities = inactiveEntityIds.length > 0;
  const hasUnsetCriteria = rulesWithUnsetCriteria.length > 0;

  // Conflicts, unset criteria, and unacknowledged gaps are blocking errors
  const isValid = !hasConflicts && !hasUnsetCriteria && (!hasCoverageGaps || gapsAcknowledged);

  return {
    isValid,
    hasConflicts,
    hasRedundancies,
    hasCoverageGaps,
    hasInactiveEntities,
    hasUnsetCriteria,
    allOverlaps: overlapResults.allOverlaps,
    entityIdsWithConflicts: overlapResults.entityIdsWithConflicts,
    entityIdsWithRedundanciesOnly: overlapResults.entityIdsWithRedundanciesOnly,
    coverageGaps,
    inactiveEntityIds,
    rulesWithUnsetCriteria,
    timestamp: Date.now(),
  };
}

/**
 * Get a human-readable summary of validation issues
 */
export function getValidationSummary(result: ValidationResult): string {
  const issues: string[] = [];

  if (result.hasConflicts) {
    const conflictCount = result.allOverlaps.filter(o => o.type === "conflict").length;
    issues.push(`${conflictCount} conflict${conflictCount > 1 ? "s" : ""} detected`);
  }

  if (result.hasUnsetCriteria) {
    issues.push(`${result.rulesWithUnsetCriteria.length} rule${result.rulesWithUnsetCriteria.length > 1 ? "s" : ""} with unset criteria`);
  }

  if (result.hasRedundancies) {
    const redundancyCount = result.allOverlaps.filter(o => o.type === "redundancy").length;
    issues.push(`${redundancyCount} redundant rule${redundancyCount > 1 ? "s" : ""} detected`);
  }

  if (result.hasCoverageGaps) {
    issues.push(`${result.coverageGaps.length} coverage gap${result.coverageGaps.length > 1 ? "s" : ""} detected`);
  }

  if (result.hasInactiveEntities) {
    issues.push(`${result.inactiveEntityIds.length} inactive entit${result.inactiveEntityIds.length > 1 ? "ies" : "y"}`);
  }

  if (issues.length === 0) {
    return "Configuration is valid";
  }

  return issues.join(", ");
}